## WebGL fluid simulation
This is a 2D fluid simulation written in TS using WebGL2. You can play around with it [here](https://jareddvw.github.io/webgl-fluid/).
- Drag your mouse around to add external forces. 
- When visualizing dye: either right-click or double-click then drag to add dye.
- press S to take a screenshot.

### Running the app
Install [pnpm](https://pnpm.io/installation) with corepack or npm, then run
```
pnpm install
pnpm run dev
```

### Gallery
<img src="https://github.com/Jareddvw/webgl-fluid/assets/91432012/9c495abd-be32-4816-9b47-569bcba19a80" alt="velocity" width="300" />
<img src="https://github.com/Jareddvw/webgl-fluid/assets/91432012/a13f0ea6-57c7-4ea8-ae84-c77297be01a2" alt="velocity" width="300" />
<img src="https://github.com/Jareddvw/webgl-fluid/assets/91432012/4cc8127a-a60f-42e1-bc81-47365082e6bd" alt="velocity" width="300" />
<img src="https://github.com/Jareddvw/webgl-fluid/assets/91432012/784c0c40-2de3-467e-bb30-45e91c0cc6d2" alt="velocity" width="300" />
<img src="https://github.com/Jareddvw/webgl-fluid/assets/91432012/d58f3804-ac9f-4e0a-a8f0-6fe5238ce438" alt="velocity" width="300" />
<img src="https://github.com/Jareddvw/webgl-fluid/assets/91432012/d5cc3ab1-043b-4df7-850d-e177e1117328" alt="velocity" width="300" />


### References
-  Harris, M. J. (2004). GPU GEMS Chapter 38, Fast Fluid Dynamics Simulation on the GPU. [link](https://developer.nvidia.com/gpugems/gpugems/part-vi-beyond-triangles/chapter-38-fast-fluid-dynamics-simulation-gpu)
-  Fluid Simulation (with WebGL Demo) [link](https://jamie-wong.com/2016/08/05/webgl-fluid-simulation/)
-  Fluids-2D: https://github.com/mharrys/fluids-2d
-  WebGL wind: https://github.com/mapbox/webgl-wind
