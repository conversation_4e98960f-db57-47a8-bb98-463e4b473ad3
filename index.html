<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon_io/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon_io/favicon-16x16.png">
    <link rel="manifest" href="/site.webmanifest">
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Waves</title>
  </head>
  <body>
    <canvas id="waves"></canvas>
    <div id="controls">
      <div class="input">
        <div id="fps"></div><a href="https://github.com/Jareddvw/webgl-fluid">code</a>
      </div>
      <div class="input">field 
        <select id="field">
          <option value="velocity">velocity</option>
          <option value="pressure">pressure</option>
          <option value="particles" selected>particles 🫨</option>
          <option value="dye">dye 🎨</option>
          <option value="image">image 🖼️</option>
        </select>
      </div>
      <div class="dye text">right-click or double-tap to add dye</div>
      <div class="input colorMode">color mode 
        <select id="colorMode">
          <option value="0" selected>rainbow</option>
          <option value="1">smoke</option>
          <option value="2">earth</option>
          <option value="3">silk</option>
        </select>
      </div>
      <div class="input image">
        upload an image
        <input type="button" value="upload" id="uploadButton">
        <input type="file" id="imageUpload" accept="image/*,video/*" style="display: none;">
      </div>
      <div id="imageDiv" class="input image"></div>
      <div id="imageParticles" class="input image">use particles <input type="checkbox" id="imageParticlesCheckbox"></div>
      <div class="input particles">particle density <input type="range" value="10" min="1" max="100" id="particleDensity"></div>
      <div class="input particles">point size <input type="number" value="2" min="1" max="5" id="pointSize"></div>
      <div class="input particles">regenerate particles <input type="checkbox" id="regenerateParticles" checked></div>
      <div class="input particles">particle trails <input type="checkbox" id="particleLines" checked></div>
      <div class="input particles trails">trail length <input type="range" value="80" min="70" max="100" id="particleTrailSize"></div>
      <div class="input" id="timeSettings">
        <input type="button" value="halt" id="halt">
        <input type="button" value="pause" id="pause">
        <input type="button" value="reset" id="reset">
      </div>
      <!-- svg icon for expand/collapse -->
      <div class="svg expanded" id="collapseButton" role="button" tabindex="0">
        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path fill-rule="evenodd" clip-rule="evenodd" d="M12 7C12.2652 7 12.5196 7.10536 12.7071 7.29289L19.7071 14.2929C20.0976 14.6834 20.0976 15.3166 19.7071 15.7071C19.3166 16.0976 18.6834 16.0976 18.2929 15.7071L12 9.41421L5.70711 15.7071C5.31658 16.0976 4.68342 16.0976 4.29289 15.7071C3.90237 15.3166 3.90237 14.6834 4.29289 14.2929L11.2929 7.29289C11.4804 7.10536 11.7348 7 12 7Z" fill="white"/>
        </svg>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
