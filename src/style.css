html, body {
  margin: 0;
  padding: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  justify-content: center;
  align-items: center;
  display: flex;
  background-color: rgb(29, 29, 29);
  --bg-color: rgba(0, 0, 0, 0.5);
}

canvas {
  display: block;
  width: 100%;
  height: 100%;

  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  border-radius: 20px;
}

#controls {
  position: absolute;
  top: 0;
  right: 0;
  padding: 20px;
  color: white;
  background-color: var(--bg-color);
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  font-family: monospace;

  transition: cubic-bezier(.11,0,.24,1) 0.25s;
}

#fps {
  padding: 5px;
  color: white;
}

select {
  background-color: inherit;
  color: white;
  padding: 5px;
}

.input, #imageDiv {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 5px;
  padding: 5px;
}

input[type="range"] {
  width: 100px;
  height: 20px;
}

input[type="number"] {
  width: 50px;
  height: 20px;
  background-color: inherit;
  color: white;
}

input[type="button"], input[type="file"] {
  padding-bottom: 2px;
  padding-top: 2px;
  height: 24px;
  background-color: inherit;
  color: white;
}

.hidden {
  visibility: hidden;
  height: 0px;
  padding: 0px;
}

#timeSettings { 
  padding-top: 10px;
}

.text {
  color: white;
  font-size: 12px;
  text-align: right;
  width: 100%;
  padding: 5px;
}

.svg {
  position: absolute;
  bottom: -20px;
  right: 0;
  background-color: var(--bg-color);
  height: 20px;
  width: 40px;
}

.svg:not(.expanded) {
  transform: rotate(180deg);
}

svg {
  width: 100%;
  height: 100%;
  color: white;
  cursor: pointer;
}

a {
  color: white;
  text-decoration: underline;
}