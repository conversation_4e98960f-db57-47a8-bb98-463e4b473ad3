
export type TextureFormat = {
    internalFormat: number;
    format: number;
    type: number;
    minFilter: number;
    magFilter: number;
}

/**
 * A class to create a framebuffer object with a texture attachment
 */
export class FBO {
    gl: WebGL2RenderingContext
    width: number
    height: number
    framebuffer: WebGLFramebuffer
    texture: WebGLTexture
    private textureFormat: TextureFormat

    constructor(gl: WebGL2RenderingContext, width: number, height: number, textureFormat?: TextureFormat) {
        this.gl = gl
        this.width = width
        this.height = height

        // Default to RGBA32F format for backward compatibility
        this.textureFormat = textureFormat || {
            internalFormat: gl.RGBA32F,
            format: gl.RGBA,
            type: gl.FLOAT,
            minFilter: gl.NEAREST,
            magFilter: gl.NEAREST
        };

        const newFB = gl.createFramebuffer()
        if (!newFB) {
            throw new Error('Error creating framebuffer')
        }
        this.framebuffer = newFB
        this.texture = this.createTexture()
        this.attachTexture()
        if (gl.checkFramebufferStatus(gl.FRAMEBUFFER) !== gl.FRAMEBUFFER_COMPLETE) {
            throw new Error('Error creating framebuffer')
        }
    }

    createTexture(): WebGLTexture {
        const { gl, width, height, textureFormat } = this
        const texture = gl.createTexture()
        if (!texture) {
            throw new Error('Error creating texture')
        }
        gl.bindTexture(gl.TEXTURE_2D, texture)

        // Check for required extensions based on format
        if (textureFormat.internalFormat === gl.RGBA32F || textureFormat.internalFormat === gl.RGBA16F ||
            textureFormat.internalFormat === gl.RG16F || textureFormat.internalFormat === gl.R16F) {
            const color_buffer_float = gl.getExtension('EXT_color_buffer_float')
            if (!color_buffer_float) {
                console.error('No EXT_color_buffer_float support')
            }
        }

        gl.texImage2D(gl.TEXTURE_2D, 0, textureFormat.internalFormat, width, height, 0,
                     textureFormat.format, textureFormat.type, null)

        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_S, gl.CLAMP_TO_EDGE)
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_WRAP_T, gl.CLAMP_TO_EDGE)
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MIN_FILTER, textureFormat.minFilter)
        gl.texParameteri(gl.TEXTURE_2D, gl.TEXTURE_MAG_FILTER, textureFormat.magFilter)
        return texture
    }

    attachTexture() {
        const { gl, framebuffer, texture } = this
        gl.bindFramebuffer(gl.FRAMEBUFFER, framebuffer)
        gl.framebufferTexture2D(gl.FRAMEBUFFER, gl.COLOR_ATTACHMENT0, gl.TEXTURE_2D, texture, 0)
        gl.bindFramebuffer(gl.FRAMEBUFFER, null)
    }

    bind() {
        this.gl.bindFramebuffer(this.gl.FRAMEBUFFER, this.framebuffer)
        this.gl.viewport(0, 0, this.width, this.height)
    }
}