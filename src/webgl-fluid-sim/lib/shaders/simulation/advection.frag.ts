export const advectFrag = /* glsl */ `#version 300 es

precision highp float;

in vec2 texCoord;

uniform sampler2D velocity;
uniform sampler2D quantity; // quantity to advect (can be velocity)
uniform float dt;
uniform float gridScale; // Grid scale
uniform vec2 texelDims; // 1 / texture dimensions for quantity
uniform vec2 velocityTexelDims; // 1 / velocity texture dimensions (for cross-resolution sampling)
uniform float aspectRatio; // canvas aspect ratio
uniform bool useBilerp;
uniform float dissipation;

out vec4 fragColor;

vec4 bilerp(sampler2D tex, vec2 coords, vec2 texelSize) {
    vec2 st = coords / texelSize - 0.5;

    vec2 base = floor(st);
    vec2 fuv = st - base;

    vec4 bl = texture(tex, (base + vec2(0.5, 0.5)) * texelSize);
    vec4 br = texture(tex, (base + vec2(1.5, 0.5)) * texelSize);
    vec4 tl = texture(tex, (base + vec2(0.5, 1.5)) * texelSize);
    vec4 tr = texture(tex, (base + vec2(1.5, 1.5)) * texelSize);

    return mix(mix(bl, br, fuv.x), mix(tl, tr, fuv.x), fuv.y);
}

// q(x, t + dt) = q(x - u(x, t) * dt, t)
void main() {
    vec2 coords = texCoord.xy;

    // Sample velocity - use hardware filtering for cross-resolution sampling
    vec2 u = texture(velocity, coords).xy;
    u.x /= aspectRatio;

    // Combine for x - u(x, t) * dt
    vec2 newPos = coords - u * dt * (1.0 / gridScale);

    if (useBilerp) {
        // Use manual bilinear interpolation with correct texel size
        fragColor = bilerp(quantity, newPos, texelDims) * (1.0 - dissipation);
        return;
    }
    // Use hardware filtering for smoother results
    fragColor = texture(quantity, newPos) * (1.0 - dissipation);
}
`